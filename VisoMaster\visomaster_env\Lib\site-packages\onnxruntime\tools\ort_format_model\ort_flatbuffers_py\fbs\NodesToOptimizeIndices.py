# automatically generated by the FlatBuffers compiler, do not modify

# namespace: fbs

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

# nodes to consider for a runtime optimization
# see corresponding type in onnxruntime/core/graph/runtime_optimization_record.h
class NodesToOptimizeIndices(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = NodesToOptimizeIndices()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsNodesToOptimizeIndices(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def NodesToOptimizeIndicesBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4F\x52\x54\x4D", size_prefixed=size_prefixed)

    # NodesToOptimizeIndices
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # NodesToOptimizeIndices
    def NodeIndices(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            a = self._tab.Vector(o)
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, a + flatbuffers.number_types.UOffsetTFlags.py_type(j * 4))
        return 0

    # NodesToOptimizeIndices
    def NodeIndicesAsNumpy(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.GetVectorAsNumpy(flatbuffers.number_types.Uint32Flags, o)
        return 0

    # NodesToOptimizeIndices
    def NodeIndicesLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # NodesToOptimizeIndices
    def NodeIndicesIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        return o == 0

    # NodesToOptimizeIndices
    def NumInputs(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # NodesToOptimizeIndices
    def NumOutputs(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # NodesToOptimizeIndices
    def HasVariadicInput(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return bool(self._tab.Get(flatbuffers.number_types.BoolFlags, o + self._tab.Pos))
        return False

    # NodesToOptimizeIndices
    def HasVariadicOutput(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            return bool(self._tab.Get(flatbuffers.number_types.BoolFlags, o + self._tab.Pos))
        return False

    # NodesToOptimizeIndices
    def NumVariadicInputs(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(14))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

    # NodesToOptimizeIndices
    def NumVariadicOutputs(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint32Flags, o + self._tab.Pos)
        return 0

def NodesToOptimizeIndicesStart(builder):
    builder.StartObject(7)

def Start(builder):
    NodesToOptimizeIndicesStart(builder)

def NodesToOptimizeIndicesAddNodeIndices(builder, nodeIndices):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(nodeIndices), 0)

def AddNodeIndices(builder, nodeIndices):
    NodesToOptimizeIndicesAddNodeIndices(builder, nodeIndices)

def NodesToOptimizeIndicesStartNodeIndicesVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def StartNodeIndicesVector(builder, numElems: int) -> int:
    return NodesToOptimizeIndicesStartNodeIndicesVector(builder, numElems)

def NodesToOptimizeIndicesAddNumInputs(builder, numInputs):
    builder.PrependUint32Slot(1, numInputs, 0)

def AddNumInputs(builder, numInputs):
    NodesToOptimizeIndicesAddNumInputs(builder, numInputs)

def NodesToOptimizeIndicesAddNumOutputs(builder, numOutputs):
    builder.PrependUint32Slot(2, numOutputs, 0)

def AddNumOutputs(builder, numOutputs):
    NodesToOptimizeIndicesAddNumOutputs(builder, numOutputs)

def NodesToOptimizeIndicesAddHasVariadicInput(builder, hasVariadicInput):
    builder.PrependBoolSlot(3, hasVariadicInput, 0)

def AddHasVariadicInput(builder, hasVariadicInput):
    NodesToOptimizeIndicesAddHasVariadicInput(builder, hasVariadicInput)

def NodesToOptimizeIndicesAddHasVariadicOutput(builder, hasVariadicOutput):
    builder.PrependBoolSlot(4, hasVariadicOutput, 0)

def AddHasVariadicOutput(builder, hasVariadicOutput):
    NodesToOptimizeIndicesAddHasVariadicOutput(builder, hasVariadicOutput)

def NodesToOptimizeIndicesAddNumVariadicInputs(builder, numVariadicInputs):
    builder.PrependUint32Slot(5, numVariadicInputs, 0)

def AddNumVariadicInputs(builder, numVariadicInputs):
    NodesToOptimizeIndicesAddNumVariadicInputs(builder, numVariadicInputs)

def NodesToOptimizeIndicesAddNumVariadicOutputs(builder, numVariadicOutputs):
    builder.PrependUint32Slot(6, numVariadicOutputs, 0)

def AddNumVariadicOutputs(builder, numVariadicOutputs):
    NodesToOptimizeIndicesAddNumVariadicOutputs(builder, numVariadicOutputs)

def NodesToOptimizeIndicesEnd(builder):
    return builder.EndObject()

def End(builder):
    return NodesToOptimizeIndicesEnd(builder)
