# automatically generated by the FlatBuffers compiler, do not modify

# namespace: fbs

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

# deprecated: no longer using kernel def hashes
class DeprecatedSessionState(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = DeprecatedSessionState()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsDeprecatedSessionState(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def DeprecatedSessionStateBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4F\x52\x54\x4D", size_prefixed=size_prefixed)

    # DeprecatedSessionState
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # DeprecatedSessionState
    def Kernels(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            from ort_flatbuffers_py.fbs.DeprecatedKernelCreateInfos import DeprecatedKernelCreateInfos
            obj = DeprecatedKernelCreateInfos()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # DeprecatedSessionState
    def SubGraphSessionStates(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            from ort_flatbuffers_py.fbs.DeprecatedSubGraphSessionState import DeprecatedSubGraphSessionState
            obj = DeprecatedSubGraphSessionState()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # DeprecatedSessionState
    def SubGraphSessionStatesLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # DeprecatedSessionState
    def SubGraphSessionStatesIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0

def DeprecatedSessionStateStart(builder):
    builder.StartObject(2)

def Start(builder):
    DeprecatedSessionStateStart(builder)

def DeprecatedSessionStateAddKernels(builder, kernels):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(kernels), 0)

def AddKernels(builder, kernels):
    DeprecatedSessionStateAddKernels(builder, kernels)

def DeprecatedSessionStateAddSubGraphSessionStates(builder, subGraphSessionStates):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(subGraphSessionStates), 0)

def AddSubGraphSessionStates(builder, subGraphSessionStates):
    DeprecatedSessionStateAddSubGraphSessionStates(builder, subGraphSessionStates)

def DeprecatedSessionStateStartSubGraphSessionStatesVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def StartSubGraphSessionStatesVector(builder, numElems: int) -> int:
    return DeprecatedSessionStateStartSubGraphSessionStatesVector(builder, numElems)

def DeprecatedSessionStateEnd(builder):
    return builder.EndObject()

def End(builder):
    return DeprecatedSessionStateEnd(builder)
