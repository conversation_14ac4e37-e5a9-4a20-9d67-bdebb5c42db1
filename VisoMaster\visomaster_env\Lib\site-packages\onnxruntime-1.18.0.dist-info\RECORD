../../Scripts/onnxruntime_test.exe,sha256=Kk4IWqVA96WJW9Q2A7A5Atvj0sxBAgaZzFTSOJElQ8M,108417
onnxruntime-1.18.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
onnxruntime-1.18.0.dist-info/METADATA,sha256=vxUUcAyZjjmej1WCEUOAAFZuA6mz84e-dquHVlNxzlk,4426
onnxruntime-1.18.0.dist-info/RECORD,,
onnxruntime-1.18.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime-1.18.0.dist-info/WHEEL,sha256=fZWyj_84lK0cA-ZNCsdwhbJl0OTrpWkxInEn424qrSs,102
onnxruntime-1.18.0.dist-info/entry_points.txt,sha256=7qLS4FbGXwPZjfdpVAGpnmk9I6m6H5CxEnwcCx1Imjs,77
onnxruntime-1.18.0.dist-info/top_level.txt,sha256=zk_fJEekrTm9DLxX2LwGegokVqP6blqPhFoMIuh0Nv8,12
onnxruntime/LICENSE,sha256=wlDWJ48LR6ZDn7dZKwi1ilXrn1NapJodtjIRw_mCtnQ,1094
onnxruntime/Privacy.md,sha256=v7dxKwdfPwfj6-5dwqKW0d4y2_ca0oZj9z0VOMtsOwg,2490
onnxruntime/ThirdPartyNotices.txt,sha256=fhblebZUcLHgp2RXQTmBoZnXFhfuKYZvfUHCOmoA_us,345046
onnxruntime/__init__.py,sha256=YQsrEUDiObQ6zlFjK-y2AOj7pU65gFk7hzxgu6Tfv0Y,4367
onnxruntime/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/backend/__init__.py,sha256=5I1Ylsawf9w6MNmK4RiN1wA-EEQqlKKwYTNZB-m_k6M,334
onnxruntime/backend/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/backend/__pycache__/backend.cpython-312.pyc,,
onnxruntime/backend/__pycache__/backend_rep.cpython-312.pyc,,
onnxruntime/backend/backend.py,sha256=SKFwZi8cQsR8HgCDpXeqMERIrqtgTHXxXrdZbIuwps0,8121
onnxruntime/backend/backend_rep.py,sha256=8Hid8lLPmcBtXsEUfpXsamX0pN5XATIIun-U7A6oNmk,1821
onnxruntime/capi/__init__.py,sha256=uRp4pMtfoayBhZgEsiFqFCD13Y6LUo82FdZsQX8X8LI,251
onnxruntime/capi/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/capi/__pycache__/_ld_preload.cpython-312.pyc,,
onnxruntime/capi/__pycache__/_pybind_state.cpython-312.pyc,,
onnxruntime/capi/__pycache__/onnxruntime_collect_build_info.cpython-312.pyc,,
onnxruntime/capi/__pycache__/onnxruntime_inference_collection.cpython-312.pyc,,
onnxruntime/capi/__pycache__/onnxruntime_validation.cpython-312.pyc,,
onnxruntime/capi/__pycache__/version_info.cpython-312.pyc,,
onnxruntime/capi/_ld_preload.py,sha256=li6cbZ64hDfUndat4mprUWzowLa3RQdw0q2E56sXFwE,413
onnxruntime/capi/_pybind_state.py,sha256=nbUpnUncwBv5pgJA8yugDYJRA4TTfC0gaYOED5jD-SA,1533
onnxruntime/capi/onnxruntime_collect_build_info.py,sha256=N7ViCgTVKYLPiHXhf16ZkGK2FVNB3PzfWFLU4ykP28w,4068
onnxruntime/capi/onnxruntime_inference_collection.py,sha256=DvLaW-VGhNIWHT0IYCoCUPIR6o7Q8jNBidOuy3a2G1A,42452
onnxruntime/capi/onnxruntime_providers_shared.dll,sha256=qHoyhEFiGPFcXIpTGCqCycf1a_BOq5kwkzXk5R9HMwE,21952
onnxruntime/capi/onnxruntime_pybind11_state.pyd,sha256=Fp8dBKH5y4DsVqsO5me-dLET060Dr3-MKrv3kVH5SCM,13639088
onnxruntime/capi/onnxruntime_validation.py,sha256=SP9G46H-OKpuy_p68r3r3qs_23yLhF2aU1mAj5Ny4pQ,6394
onnxruntime/capi/version_info.py,sha256=8mm1VTXF8xgx6N8vFNe0Tiik9qdg9Vvi9f32bPE9ktw,34
onnxruntime/datasets/__init__.py,sha256=0D1rdhXK940JccUq3Sj4BBMqjDpAPOcxlGcwJR4X3wc,471
onnxruntime/datasets/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/datasets/logreg_iris.onnx,sha256=giR4TJjXNBLZ_ZmrzVejhWi9WQmA0PvlkWRkUxxS6Pw,670
onnxruntime/datasets/mul_1.onnx,sha256=cfQxxOkyHsb76xWNAu0kBFmn3MmGc_p5pPQ5zkLvrxA,130
onnxruntime/datasets/sigmoid.onnx,sha256=U0Crpnp-NHUWKteUN4r1XxcY9V-aXXS0r2Dsx_emJLY,103
onnxruntime/quantization/CalTableFlatBuffers/KeyValue.py,sha256=e-jJFhw9fb775fDCLnWdbRSdoJ6vGD0c7qTnkIG-vNs,2250
onnxruntime/quantization/CalTableFlatBuffers/TrtTable.py,sha256=QQ9_f60Wya8U-KQOMu0gXImfhiPN6jNkfjpoCdAFic4,2665
onnxruntime/quantization/CalTableFlatBuffers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/quantization/CalTableFlatBuffers/__pycache__/KeyValue.cpython-312.pyc,,
onnxruntime/quantization/CalTableFlatBuffers/__pycache__/TrtTable.cpython-312.pyc,,
onnxruntime/quantization/CalTableFlatBuffers/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/quantization/__init__.py,sha256=eeIgS5jf18UjGelvD4Bf57Z6-Qxvg6J54V-PEtlcww0,686
onnxruntime/quantization/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/base_quantizer.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/calibrate.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/matmul_4bits_quantizer.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/matmul_bnb4_quantizer.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/onnx_model.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/onnx_quantizer.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/preprocess.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/qdq_loss_debug.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/qdq_quantizer.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/quant_utils.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/quantize.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/registry.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/shape_inference.cpython-312.pyc,,
onnxruntime/quantization/__pycache__/tensor_quant_overrides.cpython-312.pyc,,
onnxruntime/quantization/base_quantizer.py,sha256=ISPGK6UHxQaBlkgQvgUMRVVqVhlEHz3sxhjH1TcZPqg,24351
onnxruntime/quantization/calibrate.py,sha256=2n8NFxCAFZyk5tjhyD_LtCIuZaaZf18GuIcrLYR4dko,50272
onnxruntime/quantization/execution_providers/qnn/__init__.py,sha256=nKKB7VEbO574HDL2xdJPD8VeXoK2a3jd8nLBxULiVvI,120
onnxruntime/quantization/execution_providers/qnn/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/fusion_lpnorm.cpython-312.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/mixed_precision_overrides_utils.cpython-312.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/preprocess.cpython-312.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/quant_config.cpython-312.pyc,,
onnxruntime/quantization/execution_providers/qnn/fusion_lpnorm.py,sha256=vUrbMNorHH7_uKjeL1jlkPghnplPIDPz0kmN0Tt03mc,5327
onnxruntime/quantization/execution_providers/qnn/mixed_precision_overrides_utils.py,sha256=vsZC2rwLA7Z9kYUF1loF_YZP_BO0glv70Dnx8VB4SZw,19000
onnxruntime/quantization/execution_providers/qnn/preprocess.py,sha256=qLSdYvc18-u0h3CWaC8DKNWRmjdrb9Qup8nYk-DHiCM,14192
onnxruntime/quantization/execution_providers/qnn/quant_config.py,sha256=ED4yUT_TBnAwTLWF7XfLATPOpcAXM0UGMrvneXiHVY0,17829
onnxruntime/quantization/fusions/__init__.py,sha256=UMhvt6fL-eI4iadRoWpuFSktJRvNJjmGd5Rqw4nsFzY,163
onnxruntime/quantization/fusions/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/quantization/fusions/__pycache__/fusion.cpython-312.pyc,,
onnxruntime/quantization/fusions/__pycache__/fusion_gelu.cpython-312.pyc,,
onnxruntime/quantization/fusions/__pycache__/fusion_layernorm.cpython-312.pyc,,
onnxruntime/quantization/fusions/fusion.py,sha256=A6_77l5uw-hIwyoX7DPOFL6O-y3qXk-S16SMLv1Ncis,12088
onnxruntime/quantization/fusions/fusion_gelu.py,sha256=3qOO4U95ATD6S14dyC-5-vGeaQBr5U-GCsjfvHqoL98,10647
onnxruntime/quantization/fusions/fusion_layernorm.py,sha256=CKU--IH-xDUnm5qZtTK1ENYuBMnPsADUkzrOBjyW7kQ,5306
onnxruntime/quantization/matmul_4bits_quantizer.py,sha256=BT4Mp6_nJeoUmo8AZ2_POHy6IXZgQPPDX8RR18DIdOI,28348
onnxruntime/quantization/matmul_bnb4_quantizer.py,sha256=DVEK5seZJgdaN59Zwap_MyJZDQGxH_w8hhib_hz1mHQ,9300
onnxruntime/quantization/onnx_model.py,sha256=5gqbni3PsYlTZKsQG8n7UDN8BTOy38KfdsOorHP1EK8,23689
onnxruntime/quantization/onnx_quantizer.py,sha256=t7oFtAjRXE9ftrduVelVqTVXC_odhUPJ8QaUHwmcWjQ,43942
onnxruntime/quantization/operators/__init__.py,sha256=IfKXrFWtRSye1mkgD9lpwxio0fw9cVr_1CdV1cvefig,85
onnxruntime/quantization/operators/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/activation.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/argmax.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/attention.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/base_operator.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/binary_op.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/concat.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/conv.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/direct_q8.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/embed_layernorm.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/gather.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/gavgpool.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/gemm.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/lstm.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/matmul.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/maxpool.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/norm.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/pad.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/pooling.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/qdq_base_operator.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/resize.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/softmax.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/split.cpython-312.pyc,,
onnxruntime/quantization/operators/__pycache__/where.cpython-312.pyc,,
onnxruntime/quantization/operators/activation.py,sha256=JMkSthxHxIJe4wDnzhxi9nXmSdIG2Q98E7ahxXp3llM,4463
onnxruntime/quantization/operators/argmax.py,sha256=pfE9_eSTZ2otTkUcWwlLi7HJKtN10kE5c2Lz0SeVADQ,589
onnxruntime/quantization/operators/attention.py,sha256=eH7-Z3MfP6xRZCdhDAyNxWG2s2nZILxIEFVAHtqj7EQ,2637
onnxruntime/quantization/operators/base_operator.py,sha256=vrAVfKJXZvF7ZherKw4JUGonNyNuoU2TWnwBy-EQ3QE,1118
onnxruntime/quantization/operators/binary_op.py,sha256=pEQHRAS75EMp7LG6jzWV7gDQt_vzEPLJEI00eIOuoiA,2544
onnxruntime/quantization/operators/concat.py,sha256=fZFwnaqoOZ9b0ZvGpBK_MrJzVteeJguWRQ396kUh8QQ,2143
onnxruntime/quantization/operators/conv.py,sha256=KhetFXKMcFjs6W6ZnOonyy_XrSFYNr54WeFoBMmIRK8,10168
onnxruntime/quantization/operators/direct_q8.py,sha256=0-c-4O0eN2k1YJGRdG1UE1BpP0odgbmG0frHx4HV3Jk,3388
onnxruntime/quantization/operators/embed_layernorm.py,sha256=2LsZk5Um0ELaRESWjScgYyQioJelRZK6oQbzAclSgXI,4058
onnxruntime/quantization/operators/gather.py,sha256=gv6aVXEqco5cY8g0ZxomMQdwgVh591vTjznld9xfOn4,2194
onnxruntime/quantization/operators/gavgpool.py,sha256=wYyjEf3h-_QChWKnsZ2N-haBG1RSvqRitZ-Yvfwo9Dk,2445
onnxruntime/quantization/operators/gemm.py,sha256=vSMhyC_znzVl7uPzOnvHr87ReAWoGBNkZQwwse2TbN4,6252
onnxruntime/quantization/operators/lstm.py,sha256=4diaxKg7OlCA3yq1_LDLCc2oFDqr_W0zA2XdOkznPp4,5184
onnxruntime/quantization/operators/matmul.py,sha256=_NNIOMxwhIO5Cvj-izCVF7_BkBmohuiDf0hnk3sdh9M,8305
onnxruntime/quantization/operators/maxpool.py,sha256=QyDmHyBo0QKf6kNFbp2a9v6ThrBO-OL3tW0PFdN6bkI,961
onnxruntime/quantization/operators/norm.py,sha256=UivIysh8tCPpeD05PL2SLurf2ifR4nnrYneYQLG9_Vk,1643
onnxruntime/quantization/operators/pad.py,sha256=voQm5gRA6Y_MP6Dntp6D0SAdIDsuqD5fqGHI6-y22rY,4914
onnxruntime/quantization/operators/pooling.py,sha256=L0IT7G6-2XSx9-wUz5BX59Mc43FfJEg79NwW3yqEDhI,2285
onnxruntime/quantization/operators/qdq_base_operator.py,sha256=Fco9JZxrXQoVgjKvmHFuzT0mogWo9-wHiDa51CjTioo,823
onnxruntime/quantization/operators/resize.py,sha256=BMeym-7GHOSnGpZisa9BkdQkVmCXwKANA5NpnKRnaLI,962
onnxruntime/quantization/operators/softmax.py,sha256=e3ThVOh2TG1B8luG6xWkoT_hCdvsMRjvTlTje8CW-YQ,2714
onnxruntime/quantization/operators/split.py,sha256=82R65-_Rw5g23f0uekUWpA3nhOzeUWdOQgr2JZXwrOc,2258
onnxruntime/quantization/operators/where.py,sha256=wd6PQ7LlbrJTqamFMch_Fipnbt4IewMJSAPozMTrwKI,3127
onnxruntime/quantization/preprocess.py,sha256=VU4iX7g8gOgVH0zehOcOXsVWkZpx6kG_LFlwGM3Bs6c,5045
onnxruntime/quantization/qdq_loss_debug.py,sha256=bQQvqzs24zQWRM3qmI97j3LKOKdExBDZ1fzb-xMtSdo,15887
onnxruntime/quantization/qdq_quantizer.py,sha256=uagu3kHpzUhl24EXoYRrhHROKZ0BoShXJnJzLcmgVWM,54690
onnxruntime/quantization/quant_utils.py,sha256=mD-onHdO9uwmMAbHq0LkZIJXOM3JJf59Ly3-70bZi2o,30255
onnxruntime/quantization/quantize.py,sha256=yhkNNweBq7sCxIVb--JOQMRTE1_bSbZ2p_rBeS9MoW8,39055
onnxruntime/quantization/registry.py,sha256=PY89m9Dlj-y-Vkjp5zh-9B49K7UEpMSv8iwsDJWSRcI,3656
onnxruntime/quantization/shape_inference.py,sha256=9fJEciXXRizCyuVsys0GZOiwGr_d6UtqKmaqzD_p9lU,8711
onnxruntime/quantization/tensor_quant_overrides.py,sha256=8lUZht34tml0x1AkOGuIOh1PLCub9TBNiOKBd7AG2oA,21101
onnxruntime/tools/__init__.py,sha256=7up7iKcklVy6UcpIIIIlBaK690O32vaOxyaaTWvwyxU,528
onnxruntime/tools/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/tools/__pycache__/check_onnx_model_mobile_usability.cpython-312.pyc,,
onnxruntime/tools/__pycache__/convert_onnx_models_to_ort.cpython-312.pyc,,
onnxruntime/tools/__pycache__/file_utils.cpython-312.pyc,,
onnxruntime/tools/__pycache__/logger.cpython-312.pyc,,
onnxruntime/tools/__pycache__/make_dynamic_shape_fixed.cpython-312.pyc,,
onnxruntime/tools/__pycache__/offline_tuning.cpython-312.pyc,,
onnxruntime/tools/__pycache__/onnx_model_utils.cpython-312.pyc,,
onnxruntime/tools/__pycache__/onnx_randomizer.cpython-312.pyc,,
onnxruntime/tools/__pycache__/onnxruntime_test.cpython-312.pyc,,
onnxruntime/tools/__pycache__/optimize_onnx_model.cpython-312.pyc,,
onnxruntime/tools/__pycache__/pytorch_export_contrib_ops.cpython-312.pyc,,
onnxruntime/tools/__pycache__/pytorch_export_helpers.cpython-312.pyc,,
onnxruntime/tools/__pycache__/reduced_build_config_parser.cpython-312.pyc,,
onnxruntime/tools/__pycache__/symbolic_shape_infer.cpython-312.pyc,,
onnxruntime/tools/__pycache__/update_onnx_opset.cpython-312.pyc,,
onnxruntime/tools/check_onnx_model_mobile_usability.py,sha256=h-xTaXu_uSVptpmx69FiYcwACzuvI-4sTqLKkKXMo08,2871
onnxruntime/tools/convert_onnx_models_to_ort.py,sha256=I_9nhCipOpgsX5dNovCOaY3bc6NyfQLE7KuPKMzucM8,16810
onnxruntime/tools/file_utils.py,sha256=ONHY-VlxAJ7mlrTNZYkRD4I00RqsSHMZb1rUUxceQss,1569
onnxruntime/tools/logger.py,sha256=s3M5-Akb69zubXNhCpsjIoJ052gYieHV5FsOfBZ6lrI,333
onnxruntime/tools/make_dynamic_shape_fixed.py,sha256=GkbUE5kH1pOua5EJVH4trXs7mJIPIQ8T2YTeKQxr6ak,2608
onnxruntime/tools/mobile_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/mobile_helpers/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/tools/mobile_helpers/__pycache__/check_model_can_use_ort_mobile_pkg.cpython-312.pyc,,
onnxruntime/tools/mobile_helpers/__pycache__/usability_checker.cpython-312.pyc,,
onnxruntime/tools/mobile_helpers/check_model_can_use_ort_mobile_pkg.py,sha256=rqdyn3B7amoOPgpiGkPhFHaPCIZI4F58bISFq-xzBsg,12642
onnxruntime/tools/mobile_helpers/coreml_supported_ops.md,sha256=OGQFtkoXn0qA17R0o22Bb69v3qPVpqDdJNrE9HnqObI,1796
onnxruntime/tools/mobile_helpers/mobile_package.required_operators.config,sha256=nDi5sBRRAFxhelU7H6SJUEHuxiUfFRE8MIjw7sVJCXs,3069
onnxruntime/tools/mobile_helpers/nnapi_supported_ops.md,sha256=uJznEyy7ZAdlrkKQeoWFFs55rPE-kOePIJiv741r98Q,2385
onnxruntime/tools/mobile_helpers/usability_checker.py,sha256=7vxo604YSD45bUrhJRWV12P_EyAxcbz9oM4uQ7wg4b0,25977
onnxruntime/tools/offline_tuning.py,sha256=Gd120-LGX04OJZ8nvErr_8h-5XGdDOEmuJPCWVQC76E,6380
onnxruntime/tools/onnx_model_utils.py,sha256=RLeLn_0OsLRFq0rQmY4OwjXa9a5wHAxKOxA7NYxd67c,16692
onnxruntime/tools/onnx_randomizer.py,sha256=9L96dzIf59cQ2oQsmR2EEsdrR4hHwEGrpZkajEgUPAY,3361
onnxruntime/tools/onnxruntime_test.py,sha256=SvqgwrjiIpf_vsZfHmkE_FPXJkDA18mZpwYoyjMv5g0,5770
onnxruntime/tools/optimize_onnx_model.py,sha256=J6rk1Ani3VWwe0JEy5fTJ2V_zVGrA1cjIKOX6zdHd5c,1969
onnxruntime/tools/ort_format_model/__init__.py,sha256=gQqh9tWzGxeUllyIEF2FmIfee9ulji3mlJQNW7QrpJ0,1378
onnxruntime/tools/ort_format_model/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/operator_type_usage_processors.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/ort_model_processor.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/types.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/utils.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/operator_type_usage_processors.py,sha256=B5sRgMebZw3bsY2EkPQyJ57DCQA5UVwx4ncR98a_YPU,27211
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgType.py,sha256=ErRXrmwza1xgVW4OAENw_B8yVc7WfWbAYYhiAO3Cc2g,147
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgTypeAndIndex.py,sha256=8Sx0mmUqjN4uN7MeTY6scIgeZO6gruARh8MvlEQdUdQ,2093
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Attribute.py,sha256=S5SA5y9FB2HWcPIPx99jz_ebTV85O8cTETJgGe_FKLo,11187
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/AttributeType.py,sha256=piF8u_U7zNE5eSrwcQhRLgvr2axK21-455McxIje2HQ,346
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Checkpoint.py,sha256=0cZ14luQy_w8GlbDF_xoGPWtee2UueQM4jt59YX5B9o,4342
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedKernelCreateInfos.py,sha256=K6Sl9lPxPE8huGE0tunG9MXDFYbzJs5Hc4nzKj8ft88,4648
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedNodeIndexAndKernelDefHash.py,sha256=Lr3cs1e80M1zLbZ1hBKwDsSl6d8zxjkbIfAguiTkJko,2526
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSessionState.py,sha256=N5CN72wJObCmYGcbA5z9o3u1khYxJ9bVSIaFIJCDR3E,3678
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSubGraphSessionState.py,sha256=ARjFKPJX_pB2KTrSUCHz_iN_UmrB3xRDKtUbIepTX-Q,2682
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Dimension.py,sha256=VHQcCV5ip0BIhm1EFtsAXA-uMp8tqlu1BfiJP2LZuQc,2262
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValue.py,sha256=14W1geQj5_V_5UiqgrG2oFGDgCkAIS5d71xemZwmO_Y,2574
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValueType.py,sha256=2kmuPePZe_rWSMkI7ceIhewHL3wvvsDEhzH-LBusm0M,174
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/EdgeEnd.py,sha256=aNdY1SA8fDyvH1Vsqo5xklVuhGtw2sUmyy86HmtWEbk,1137
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/FloatProperty.py,sha256=qqpRpfy9QXs7WHhHUARUjv5q_zs8wiT2-iOjKD9v4NQ,2075
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Graph.py,sha256=Y_AnjssgrJ7o4oU1w-KG-rOl8EE8YuEnvDWu7rGN57E,11039
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/InferenceSession.py,sha256=0FgbOyXfWicKshG131GzHqPTFJvoVorBxTVP1xM6ZNU,3125
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/IntProperty.py,sha256=EjWio-yFwfkE_N764bYTaDRz0EHSZ1n0JePkLSNGitk,2037
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrArgsEntry.py,sha256=lKy6VMEjvkiv5BGOjugWEZtlobNMwvlfcSRSYzjCiAk,3193
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrResolver.py,sha256=cufEs5uTENEF6bvZQemzDztbErNtvDuzMpDoCnSBpik,2867
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/MapType.py,sha256=wlC76nL8dC0vdZVlEoUC9qwgbVDwnSt5RCRO5r_Wfmg,2194
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Model.py,sha256=CPbe2q2OcKU8LbShHGtnIG_jrW83f3nvMR5Trg1Nx90,7663
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ModuleState.py,sha256=rGMagBBqyCNbewI-xUygxN07m7fLDQmH2EWISmFd7ss,4994
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Node.py,sha256=IjM4YjGq_swHTiLw01xsSWmAYaZmkVZUR3cei3byY68,10718
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeEdge.py,sha256=LiB9vyQTsFkfRRTuiDcwrRfVaWrRzAALP5Rop0rtGKg,4183
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeType.py,sha256=bBLbvDWziLLRxhjCAqlS44k_hdIWbWk3vsRxCDOLz7k,151
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodesToOptimizeIndices.py,sha256=KoWmACgRnK8qGNyXiKo6NywUvzpvYYxGQ0AyayjyaFQ,6144
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OpIdKernelTypeStrArgsEntry.py,sha256=6WV2Pj3vkqJ2NZ19VWrPAxKtoGCeiJ6NTVOvs89BIQ4,3387
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OperatorSetId.py,sha256=UOiU-CCvexY3OOWk2hiZyBI0OItcA6jiZTsERDZxu2U,2099
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OptimizerGroup.py,sha256=Xri5OFTQ881fzaVdQZjVviLyE5vH2aFGGArQE5H1ZbU,4135
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ParameterOptimizerState.py,sha256=AIEaCRcEln5wC7X3C8vp7TmmpxuaH7t93UCphxbxRJA,3218
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/PropertyBag.py,sha256=QXs064QNug8pG9zjX9aWEPmO63CIWJcL-kMLgMPk7vI,5099
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecord.py,sha256=Lrl5lCzbUL323Y1nnij-7vn95d3qNHkiVPwkbg6SSds,4067
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecordContainerEntry.py,sha256=PM7_R0kbnF3K32qoLOIPXmeudQfj0pvabMQLB-FjREw,3832
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizations.py,sha256=qYkC65wMqu7FinW2X9pPTeRD4bY7xdIsOfXnIJoFDFU,2800
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SequenceType.py,sha256=iiMOzyjzXzjO3zoad_oG2-ct786Wv8u4LWoFOZdmc0A,1829
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Shape.py,sha256=4Y6yU35JHSXKzb2BDzObkss4yiopwS3IXkSBpFf88FI,2352
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SparseTensor.py,sha256=3EZahzlyO_73WOzK9jZHwoVqsQkq3407ATBOG56KLEE,3806
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringProperty.py,sha256=Zd5QCcSgvf5ojuOBRV19gGyQPhqjJkoBaMdZ06kOc7c,2110
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringStringEntry.py,sha256=Iih7xRq4zvu4cVV4_TAC4sbjaH-dLqZGU68X0oLaWoI,2147
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Tensor.py,sha256=CZItxbQ-B9cJnaS5HwBNWT1buat_JgX-W-zOn9m11a4,6802
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorDataType.py,sha256=WmblM8GoLES62L12Ea3dC655_ruO2T3R2Q085OLCFc8,500
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorTypeAndShape.py,sha256=noUmY1JdkO-Bjhghyuevf7cy0Gokhnh0r_AcmIt1fl4,2326
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfo.py,sha256=ToFYZzH0Vit5VVIztodfzP-fCOc97vgBHXz6Y_tHyWk,2599
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfoValue.py,sha256=a2VKzgcR6v4qiffa5a5HYNUaHKDLdmsFbAdsNwrmT5Q,198
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ValueInfo.py,sha256=ZC6peiqYRYKYw_si4BmtZUbRRLf9BkkD4za4xky3QHY,2655
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__init__.py,sha256=EfkIrreUF6TrcYpBo1NJ8GOV_p_o_YXg3fSptBN5XUo,251
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ArgType.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ArgTypeAndIndex.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Attribute.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/AttributeType.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Checkpoint.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedKernelCreateInfos.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedNodeIndexAndKernelDefHash.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedSessionState.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedSubGraphSessionState.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Dimension.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DimensionValue.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DimensionValueType.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/EdgeEnd.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/FloatProperty.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Graph.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/InferenceSession.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/IntProperty.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/KernelTypeStrArgsEntry.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/KernelTypeStrResolver.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/MapType.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Model.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ModuleState.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Node.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodeEdge.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodeType.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodesToOptimizeIndices.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OpIdKernelTypeStrArgsEntry.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OperatorSetId.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OptimizerGroup.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ParameterOptimizerState.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/PropertyBag.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizationRecord.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizationRecordContainerEntry.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizations.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/SequenceType.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Shape.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/SparseTensor.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/StringProperty.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/StringStringEntry.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Tensor.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TensorDataType.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TensorTypeAndShape.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TypeInfo.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TypeInfoValue.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ValueInfo.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/tools/ort_format_model/ort_model_processor.py,sha256=Zh07RmcGYf0-qbXRiim2DWUGHbsrEeG6_rOcx3VLexw,4472
onnxruntime/tools/ort_format_model/types.py,sha256=s32mQkFeWRdu3EzC1qd-lxhIvLQ1GOohyHgbslGcMes,4466
onnxruntime/tools/ort_format_model/utils.py,sha256=Ix5mFXZCnMEHf8Tg7Mwg2GFdy0d1l-zocT2fsE8_8sU,2604
onnxruntime/tools/pytorch_export_contrib_ops.py,sha256=xxlw5jPDy72tWEPPYn8Qhof4H-edK7RwpT0ZXtWYfC4,4091
onnxruntime/tools/pytorch_export_helpers.py,sha256=MRegHn3z3VhVbZQ4O-kTGedIE-pufyxhq1A1GVIdCjY,5971
onnxruntime/tools/qdq_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/qdq_helpers/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/tools/qdq_helpers/__pycache__/optimize_qdq_model.cpython-312.pyc,,
onnxruntime/tools/qdq_helpers/optimize_qdq_model.py,sha256=9kpU0Dukc0ZEHnjwO7NqNFUFVRtOx8xgFPVWZpXkEcQ,1279
onnxruntime/tools/reduced_build_config_parser.py,sha256=O9XtpCRKoFiPcXuwfyGH1zcvpVU0cbOq9JxFh0Jm-Fs,10137
onnxruntime/tools/symbolic_shape_infer.py,sha256=RYPlP39bsnWiptPi0Sgj6dEFuYjerxWcu7Ut4gJ_9qw,141288
onnxruntime/tools/update_onnx_opset.py,sha256=fplb1ypV-pFhu8Xsi5u_bDfI7EsC4zamJkTziccgQ2c,1182
onnxruntime/transformers/__init__.py,sha256=2c213CqzXrc0N6Cqf__Te5d_SH_stfLdNdeNrugB7SQ,321
onnxruntime/transformers/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/affinity_helper.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/benchmark.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/benchmark_helper.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/bert_perf_test.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/bert_test_data.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/compare_bert_results.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/constants.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/convert_generation.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/convert_tf_models_to_pytorch.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/convert_to_packing_mode.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/dynamo_onnx_helper.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/float16.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_clip.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_unet.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_vae.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_bart_attention.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_base.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_bias_add.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_biasgelu.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_biassplitgelu.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_conformer_attention.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_embedlayer.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_fastgelu.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_gelu.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_gelu_approximation.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_gemmfastgelu.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_gpt_attention.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_gpt_attention_megatron.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_gpt_attention_no_past.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_group_norm.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_layernorm.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_nhwc_conv.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_options.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_attention.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_gelu.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_layernorm.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_matmul.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_reshape.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_rotary_attention.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_shape.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_simplified_layernorm.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_skip_group_norm.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_skiplayernorm.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_transpose.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/fusion_utils.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/huggingface_models.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/import_utils.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/io_binding_helper.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/large_model_exporter.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/machine_info.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/metrics.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_exporter.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bart.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bert.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bert_keras.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bert_tf.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_clip.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_conformer.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_gpt2.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_phi.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_t5.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_tnlr.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_unet.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_vae.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/onnx_utils.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/optimizer.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/profiler.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/quantize_helper.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/shape_infer_helper.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/shape_optimizer.cpython-312.pyc,,
onnxruntime/transformers/__pycache__/torch_onnx_export_helper.cpython-312.pyc,,
onnxruntime/transformers/affinity_helper.py,sha256=KOKBvzoBr-wOk0QHMGKzY1uy1iI7E6eHpYwBdTHM-Y4,1442
onnxruntime/transformers/benchmark.py,sha256=s21Aq5ialtyh_g9P2i4yv2YDTzYZ2ObIB5m8h4KFToM,33741
onnxruntime/transformers/benchmark_helper.py,sha256=umWUsxNc2IkQbTn9bXtaObOvitY8fgW78KBsbzyqkE4,23245
onnxruntime/transformers/bert_perf_test.py,sha256=P4N62-17JrwyIubnZS4vcne6AJ9k0FOCTIacAEqjfyU,20995
onnxruntime/transformers/bert_test_data.py,sha256=x1my7LUl2OkiaRFxxXwvbYIisSyK7QUsM5sYiE6dcKA,23516
onnxruntime/transformers/compare_bert_results.py,sha256=ZtnITLB0Wy10npJE4mAMGWvFSYdQb8BumJg3J8CPEwc,7908
onnxruntime/transformers/constants.py,sha256=UfbiXD1CKrr9Rza6gBI7VbLT-FojgPuKLWs6GyBS_hE,1143
onnxruntime/transformers/convert_generation.py,sha256=A2E0RGGeTW60FCET7medSXng9vTBmteUXfEtpjvNpEE,127566
onnxruntime/transformers/convert_tf_models_to_pytorch.py,sha256=JrMGzUi_4cMfYoIYrxa1n0jnMDG-WYj-xmUXZmH8aJ0,6705
onnxruntime/transformers/convert_to_packing_mode.py,sha256=TGLK5ZwYvx79S8kJOsR78ydxY65YEe-OudhWvs-NbIU,16909
onnxruntime/transformers/dynamo_onnx_helper.py,sha256=wrSGLiZfQBMQhborPj3kFzMwSwWIdtI0Z0AKiwyoMjM,3797
onnxruntime/transformers/float16.py,sha256=GumnXmhSqmQsk1avBLok0fNHy1Q7mnO5XrNN31CbRJE,24691
onnxruntime/transformers/fusion_attention.py,sha256=XVhct9HjfVWmyjUKZR9gbJZRUGjOoBOvp9-D9P_WDIE,52559
onnxruntime/transformers/fusion_attention_clip.py,sha256=hZRHEPC1yQa43jiCL9nf8CRxwqs-wtQpNXe8NU_qKps,8722
onnxruntime/transformers/fusion_attention_unet.py,sha256=b4FGGJGDDzIPtCO_WySiUNeiFHKODAwHScxIDDBh6Kc,56932
onnxruntime/transformers/fusion_attention_vae.py,sha256=Ju-PG2LCnNM0KNmzbw4zXKwNkGxP4UOsawoJQ8WTbRw,12418
onnxruntime/transformers/fusion_bart_attention.py,sha256=3AcCTwHuVdl6gE9QTV_55HgTSCjPk41KaKxap5hfU9s,29437
onnxruntime/transformers/fusion_base.py,sha256=B8XFObxBIe6fv6lPKFHw8H-zIxOlNvNNRe67YJtFdmc,5870
onnxruntime/transformers/fusion_bias_add.py,sha256=7JRHl-p1M8GxNfa9cgHsES7dwburpaTWqYh921_8QjQ,2066
onnxruntime/transformers/fusion_biasgelu.py,sha256=vGamxthOu6jXsxCRVdTFaP25-_tnjz9TVq91pIRV_Is,2300
onnxruntime/transformers/fusion_biassplitgelu.py,sha256=6G73bmAGM5y02Rm_Lupbn341O0Y5Sr-2Re_628Ez2Qo,4516
onnxruntime/transformers/fusion_conformer_attention.py,sha256=-HRarRSTQEwq-XeDoRdzH9RY3konlVfhj-jtR9twOjU,5021
onnxruntime/transformers/fusion_embedlayer.py,sha256=Z7zXb7UYkgQO_Zikpnoj97KOoFYIHEm74yVvh-zf8kM,36750
onnxruntime/transformers/fusion_fastgelu.py,sha256=pi2U93F4xWMThs6Yz9K1d6AZQ1kyWSZhjeGqs7WWAVI,13324
onnxruntime/transformers/fusion_gelu.py,sha256=GrTB0LoVz_YRyTW-JoL4Fh_fz_IA01JweEP0Tj_Lwgs,10180
onnxruntime/transformers/fusion_gelu_approximation.py,sha256=Xsa2v5mHjEuZrwnf1bm3UCCJ8I1is0dmuzzXgf5zDl4,1029
onnxruntime/transformers/fusion_gemmfastgelu.py,sha256=jBQ1qx6rOJY-qY_35_HFlEjsp3aDuT7GSyXQqyXSQ4s,4258
onnxruntime/transformers/fusion_gpt_attention.py,sha256=20ZhplkAVJ3rq1VWwcNRmRs6OZu7lTHKIop3SAyDSUw,22508
onnxruntime/transformers/fusion_gpt_attention_megatron.py,sha256=HhoweTBxleb1niPOU_cfQzvUwM4LjxCVuZZWVEy3Imw,13639
onnxruntime/transformers/fusion_gpt_attention_no_past.py,sha256=qQb8WekiDJeQUV8egoCTrLoZki018veZTVVE-w3p3ds,10794
onnxruntime/transformers/fusion_group_norm.py,sha256=AUCHVK9FWmzSjIPFVIv194Qlure8xmFCyTKqcrQkkFA,7604
onnxruntime/transformers/fusion_layernorm.py,sha256=CZknSEugEcncfVwdIJSUGPzhAHts7mBakBKkfYq3nVU,12217
onnxruntime/transformers/fusion_nhwc_conv.py,sha256=xHP6QT4F-K7z3Cm_5zh1aPqE0UkDwB0votQbqsxJeZg,3973
onnxruntime/transformers/fusion_options.py,sha256=NSeQVc9Hz9S-4uGpxuYfsigzdLDOdsRqXyujbfRPGhw,12704
onnxruntime/transformers/fusion_qordered_attention.py,sha256=VutuLlHX0oDnDhcbzWhVSq-VXlyKNaOXu2hW8gdn21c,17163
onnxruntime/transformers/fusion_qordered_gelu.py,sha256=V6BhjvA5rMpLOGw2bPQrierztLI_P_O99I9gLmy0nxM,4435
onnxruntime/transformers/fusion_qordered_layernorm.py,sha256=dq5odT_lrs8zWZZuADDCPDOGBfE8JLo5n5jI2obtT3U,4957
onnxruntime/transformers/fusion_qordered_matmul.py,sha256=j85chtrY9YrGD1ERNIHqCBAxZW51I2Sk_EFU4jg8qdM,8566
onnxruntime/transformers/fusion_reshape.py,sha256=AfT88v22G6PgZPzVKM39_QduUnlIbe8dbxvPCh-5dkg,6403
onnxruntime/transformers/fusion_rotary_attention.py,sha256=tMuoiLV_Hv769vW7hlPdMpadleCix6GeLBoZGWSSxYg,68261
onnxruntime/transformers/fusion_shape.py,sha256=EpmvtrdKOCD914c-tOqXzlZBHoN2mvc06R3MErCkfiw,3813
onnxruntime/transformers/fusion_simplified_layernorm.py,sha256=KfU0Vs8XB7U5DzSAgDOilBGhEXhWc-vPbUDDz0MYVSI,6554
onnxruntime/transformers/fusion_skip_group_norm.py,sha256=57T3O7C5pbama_5nQhiuOPbGnj2cAi9xHeoF8Fwqd_E,10880
onnxruntime/transformers/fusion_skiplayernorm.py,sha256=NQuxs5_3p1SG12d6La8pNX-f1Ep8XfzdnGfC8vP3uhM,8639
onnxruntime/transformers/fusion_transpose.py,sha256=TxoWRd7ItEt1CbgUeKVqgqQyAt8_S8thA6Et73_kDCs,7035
onnxruntime/transformers/fusion_utils.py,sha256=yZMl3zVzkemljAwv8tPFWmJ4XUUF9Y1a2usJL6Ym1nU,12775
onnxruntime/transformers/huggingface_models.py,sha256=C0B3Lh52edigeZd_JZEBBl2x_hruGF0u5VmsNOke_J4,9130
onnxruntime/transformers/import_utils.py,sha256=_ILscQRcSyaJHt1l6jqcny5FWy7Qr6N_7hKs5aav8oM,651
onnxruntime/transformers/io_binding_helper.py,sha256=_rAfHoglqsMOH4WIcHaS1_mXtkwJPZQ9s-XnZE46juo,17543
onnxruntime/transformers/large_model_exporter.py,sha256=dIQBjuu8YBh6DYAV-vgoAqiZe0Prb2nsZoVZEW0yf1Q,15310
onnxruntime/transformers/machine_info.py,sha256=wAFDfkm-y_d5SfVQ7iaI_16QjpAvMrGPXHgNCoPBLiI,7282
onnxruntime/transformers/metrics.py,sha256=I03M327XxrOgj4sLl-AzD5k9n2BlqGpF-cJfamEmHhA,5327
onnxruntime/transformers/models/bart/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/bart/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/transformers/models/bart/__pycache__/export.cpython-312.pyc,,
onnxruntime/transformers/models/bart/export.py,sha256=PNlhkbvrxTxSSLXpzqoa02Lektzf8rdZpcVFBxw-qcI,4285
onnxruntime/transformers/models/bert/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/bert/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/transformers/models/bert/__pycache__/eval_squad.cpython-312.pyc,,
onnxruntime/transformers/models/bert/eval_squad.py,sha256=hE5_03D6TBJJWcicVvJCSTWvvJTBGzFEqGw_6Y51Z0M,12378
onnxruntime/transformers/models/gpt2/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/gpt2/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/benchmark_gpt2.cpython-312.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/convert_to_onnx.cpython-312.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/gpt2_helper.cpython-312.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/gpt2_parity.cpython-312.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/gpt2_tester.cpython-312.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/parity_check_helper.cpython-312.pyc,,
onnxruntime/transformers/models/gpt2/benchmark_gpt2.py,sha256=MHPnn2HHYEJo4yECNDAfeMBGLeOX-OVYi1jxZLLgPsI,15930
onnxruntime/transformers/models/gpt2/convert_to_onnx.py,sha256=7KR2nN6UJ86TsMNhhqT2fBsnb3LzfPvBrIUOFwUv_X4,20593
onnxruntime/transformers/models/gpt2/gpt2_helper.py,sha256=PuV3Jbk1XPnk_jH8bs37n5GhRRpYqr_kG04rlV3i62M,41381
onnxruntime/transformers/models/gpt2/gpt2_parity.py,sha256=iRMcaSLzmcCxeWfBaBf_GTWa-DXkcG8mCir2QMXyUzg,18238
onnxruntime/transformers/models/gpt2/gpt2_tester.py,sha256=cZK0nIL4BToALBE86sPj3Rm9mNVC4BIe5GQPmmZj6zA,20020
onnxruntime/transformers/models/gpt2/parity_check_helper.py,sha256=jU3bTPvyKgHqxrGIce12_LbqaXC688XnBnBp5AHz_ZM,5806
onnxruntime/transformers/models/llama/__init__.py,sha256=yR2FucNw-jt_3CbNt-zuM7DmldPq1rJK3SV8gRISzN0,490
onnxruntime/transformers/models/llama/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/transformers/models/llama/__pycache__/benchmark.cpython-312.pyc,,
onnxruntime/transformers/models/llama/__pycache__/benchmark_all.cpython-312.pyc,,
onnxruntime/transformers/models/llama/__pycache__/benchmark_e2e.cpython-312.pyc,,
onnxruntime/transformers/models/llama/__pycache__/convert_to_onnx.cpython-312.pyc,,
onnxruntime/transformers/models/llama/__pycache__/dist_settings.cpython-312.pyc,,
onnxruntime/transformers/models/llama/__pycache__/llama_inputs.cpython-312.pyc,,
onnxruntime/transformers/models/llama/__pycache__/llama_parity.cpython-312.pyc,,
onnxruntime/transformers/models/llama/__pycache__/llama_torch.cpython-312.pyc,,
onnxruntime/transformers/models/llama/__pycache__/quant_kv_dataloader.cpython-312.pyc,,
onnxruntime/transformers/models/llama/benchmark.py,sha256=gnjPEQvekmFuCLzOtNowA5WWtr5MK9YD5pwkYDQCYSw,27262
onnxruntime/transformers/models/llama/benchmark_all.py,sha256=dBhvMrWfCYL768jv7C6r3kFZkIZoVknM-uodKsAgA-k,15851
onnxruntime/transformers/models/llama/benchmark_e2e.py,sha256=wGOB-xEmI3aGy4bNn_qwz_ElHy9P0Jvy2DX6o_8r7-Y,24221
onnxruntime/transformers/models/llama/convert_to_onnx.py,sha256=dwoZIRZSD_Pz2XdHmbzl0-AYBeSghlsUQzNFRuds0oE,43492
onnxruntime/transformers/models/llama/dist_settings.py,sha256=4rLhv9WYMsAeSYTlLJfvGn3BlnUoXSGSEh3ORCmgpgc,1636
onnxruntime/transformers/models/llama/llama_inputs.py,sha256=weNcvmNxOdy6zG1CZMmy5HnkOtpNFKEZtAwC1ZUJpIA,21005
onnxruntime/transformers/models/llama/llama_parity.py,sha256=dWAqlbDds0zmgcnIqHUpSNQpqbwvUSscCzO9taflxcc,10236
onnxruntime/transformers/models/llama/llama_torch.py,sha256=jhp3ladbXlo45w11ocUuU_QVIHdy77oNSFyu_tUnpbk,1665
onnxruntime/transformers/models/llama/quant_kv_dataloader.py,sha256=piVldpGm9eBmF4wzgmKJprhujqTPddqORxZyLizcJdA,4959
onnxruntime/transformers/models/longformer/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/longformer/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/benchmark_longformer.cpython-312.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/convert_to_onnx.cpython-312.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/generate_test_data.cpython-312.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/longformer_helper.cpython-312.pyc,,
onnxruntime/transformers/models/longformer/benchmark_longformer.py,sha256=oaYEyUGnTBjXdayIMaKR6UGuCOsfn9FYeyNjin13oEY,30250
onnxruntime/transformers/models/longformer/convert_to_onnx.py,sha256=cTmSpSZhytBrM40Ys1r4FCUctyovXS3_e40_iozD4Bk,15219
onnxruntime/transformers/models/longformer/generate_test_data.py,sha256=wQxpgo_vZBhKRlquJwUB9FH3_xxvyDC3aCCZdkvADLM,9964
onnxruntime/transformers/models/longformer/longformer_helper.py,sha256=FH7Uykc57rLNr1l0pr85OVgr9PZE_4x29xdE-t1riC4,3180
onnxruntime/transformers/models/phi2/__init__.py,sha256=yR2FucNw-jt_3CbNt-zuM7DmldPq1rJK3SV8gRISzN0,490
onnxruntime/transformers/models/phi2/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/transformers/models/phi2/__pycache__/convert_to_onnx.cpython-312.pyc,,
onnxruntime/transformers/models/phi2/__pycache__/inference_example.cpython-312.pyc,,
onnxruntime/transformers/models/phi2/convert_to_onnx.py,sha256=OZeFmtIYKrSYGkipD1-xcIYR8ryJfGUWqW2coqMZ2zo,20376
onnxruntime/transformers/models/phi2/inference_example.py,sha256=xZBHx3iFJ8jQvvNxFZTdYAFLxdOPYcldmk53ktgcjew,17700
onnxruntime/transformers/models/stable_diffusion/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/stable_diffusion/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/benchmark.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/benchmark_controlnet.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_txt2img.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_txt2img_xl.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_utils.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/diffusion_models.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/diffusion_schedulers.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_ort_cuda.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_ort_trt.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_tensorrt.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_torch.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/optimize_pipeline.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/ort_optimizer.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/pipeline_stable_diffusion.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/trt_utilities.cpython-312.pyc,,
onnxruntime/transformers/models/stable_diffusion/benchmark.py,sha256=hTMR3Jade2ce1V0NYM1imvImP3dVThIkU1y_2Zw_TJA,48353
onnxruntime/transformers/models/stable_diffusion/benchmark_controlnet.py,sha256=0wLUcJMeSzrDOynjT30wC-8ncukGmgEnv5ngv9rFNk0,13253
onnxruntime/transformers/models/stable_diffusion/demo_txt2img.py,sha256=k_H66rQBSryX5MvHzheAcKq5pXIMPeoktzxP-EKfrPw,3394
onnxruntime/transformers/models/stable_diffusion/demo_txt2img_xl.py,sha256=F1DGjYE-kDuYdBn9ZMPT5Lc7jJNsCs_ZkTT5JgcGmD8,10179
onnxruntime/transformers/models/stable_diffusion/demo_utils.py,sha256=yk5Bw1Pu3RAluY_UE5Z1FvmjU8vQt8aQp0Low2X3tI0,29367
onnxruntime/transformers/models/stable_diffusion/diffusion_models.py,sha256=wbQLaKqh4q4_LVK5cwzbFI-r0m3zuw8AMB_o9CfRpK4,51710
onnxruntime/transformers/models/stable_diffusion/diffusion_schedulers.py,sha256=liNQ-O8mXkVDd5BWACjV58Fg6kVx_gWf6hp0mmySKzw,49538
onnxruntime/transformers/models/stable_diffusion/engine_builder.py,sha256=8VkzQCo_UwgU6GeMVXcm9QbZSz09gsv6o2TtFwSG7os,11980
onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_cuda.py,sha256=hFXQIYGDYFzg8Vi4OVAcUknkHnwFe7B4gfEcifyZfZw,16294
onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_trt.py,sha256=0M-JLT3Z1zYPEVkJ0TPCZuhbIFCstbBi5Wh623VLcww,11451
onnxruntime/transformers/models/stable_diffusion/engine_builder_tensorrt.py,sha256=LSoAcwy4nwFQ4aikwJimlAQl1iXAWX4nIbcWD2H7qPw,15999
onnxruntime/transformers/models/stable_diffusion/engine_builder_torch.py,sha256=4SeIgxcd7Vv3WuSpKcklESud8-O6tZKDVpFssNCzUTg,4289
onnxruntime/transformers/models/stable_diffusion/optimize_pipeline.py,sha256=LocY1eyRHdTGaTSjzBSLIA2-AnfSszfk7Lp4kr24vBE,12881
onnxruntime/transformers/models/stable_diffusion/ort_optimizer.py,sha256=Xi35IWxLtzTcXBhffGeOx2ltV_xCsjAzz7BBwi78mDE,5836
onnxruntime/transformers/models/stable_diffusion/pipeline_stable_diffusion.py,sha256=TerkRHaxgs3HhSdT4YisYO_bijBBDZ3RmbUZojmosrw,33995
onnxruntime/transformers/models/stable_diffusion/trt_utilities.py,sha256=XZCfqG_kZ72e-L9p7PlGqc4NLvFZF1h40A6Guyj6z8k,432
onnxruntime/transformers/models/t5/__init__.py,sha256=F8Gml7gD7jmMyMIkKvHiXLfj7kWyz175X1-5_iaFx5k,495
onnxruntime/transformers/models/t5/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/transformers/models/t5/__pycache__/convert_to_onnx.cpython-312.pyc,,
onnxruntime/transformers/models/t5/__pycache__/past_helper.cpython-312.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_decoder.cpython-312.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_encoder.cpython-312.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_encoder_decoder_init.cpython-312.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_helper.cpython-312.pyc,,
onnxruntime/transformers/models/t5/convert_to_onnx.py,sha256=6n8mojdaT-4PtVJyQeUYboIzJcIljjaL3xdVmigUDaM,9010
onnxruntime/transformers/models/t5/past_helper.py,sha256=ounFkzTPTM0N9gjZ70jhh-grskckMQwCu2KsDupljpM,6987
onnxruntime/transformers/models/t5/t5_decoder.py,sha256=jboQOt9bzfIYj5RiHr5c9Le8_xM2iCTy9a7ZoqDoQsY,17262
onnxruntime/transformers/models/t5/t5_encoder.py,sha256=iTbEUNf9zE8wtrRYkvJ7mfSfXmGvwuDlcZTjKKhFRfE,6295
onnxruntime/transformers/models/t5/t5_encoder_decoder_init.py,sha256=13eGZw0soWdbjzpldIfsBeEYyYCF3jFDSbaaeai25U0,12273
onnxruntime/transformers/models/t5/t5_helper.py,sha256=GfXBXQ8PhJIvQxXBF3UajyQcKeAksYJwcvBVE1tjje8,11032
onnxruntime/transformers/models/whisper/__init__.py,sha256=yR2FucNw-jt_3CbNt-zuM7DmldPq1rJK3SV8gRISzN0,490
onnxruntime/transformers/models/whisper/__pycache__/__init__.cpython-312.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/benchmark.cpython-312.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/benchmark_all.cpython-312.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/convert_to_onnx.cpython-312.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_chain.cpython-312.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_decoder.cpython-312.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_encoder.cpython-312.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_encoder_decoder_init.cpython-312.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_helper.cpython-312.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_openai_helper.cpython-312.pyc,,
onnxruntime/transformers/models/whisper/benchmark.py,sha256=Trl33hh_AudH13Ev3okjS3gEtL2Fd-BrKXj3vAnfZXc,23376
onnxruntime/transformers/models/whisper/benchmark_all.py,sha256=I5o1O0-rKc1Z6RyE5mp51rHLyTdGFi-MoJIEZiB6m30,19461
onnxruntime/transformers/models/whisper/convert_to_onnx.py,sha256=lD3GYfFviG03OJ_QRMNtgLsAjEldoBziUslkkHxhZow,18404
onnxruntime/transformers/models/whisper/whisper_chain.py,sha256=r4csnoac1FiZh--2oHOwKqar4i-Uhe6TJ9ejkGE-PSQ,14910
onnxruntime/transformers/models/whisper/whisper_decoder.py,sha256=8vqR-FefnQuFpgTOZHV7LUUG5sbZB0w_Cy_LzdjCn0c,16021
onnxruntime/transformers/models/whisper/whisper_encoder.py,sha256=6LyQmT-JasSzfYRqG4Mr9Cg5vp60TqTt22q3__Ey700,5740
onnxruntime/transformers/models/whisper/whisper_encoder_decoder_init.py,sha256=TA27lOA_Fd-j3yulAf3773Fv9UnyZYHWveqbJiAYCDg,12723
onnxruntime/transformers/models/whisper/whisper_helper.py,sha256=VatGmGiB0NJr1m1y5IqSLkWamQhj-ygmmL-9I-0lgxM,23487
onnxruntime/transformers/models/whisper/whisper_openai_helper.py,sha256=uF1MpfwD8LWFmA6-tWLq10ocB-yVFx_7NA_dL3Rsy0g,3272
onnxruntime/transformers/onnx_exporter.py,sha256=DvUj1-ozpVwNaY6bCF1u1byZTHWDtRJbd3ClA2opRI8,25176
onnxruntime/transformers/onnx_model.py,sha256=HZeIHif1IqGWa2tkYa719ZoI7xMPgtWJ62JY_lrNbow,64986
onnxruntime/transformers/onnx_model_bart.py,sha256=M_5C_iYSFhaJgtvtKEViM25Y_haeikC1XL-DekTFh1o,5579
onnxruntime/transformers/onnx_model_bert.py,sha256=1pux4Dfi1WnPO7_fuNVPwM57vXCUaYILMvTQQUx8lhQ,19974
onnxruntime/transformers/onnx_model_bert_keras.py,sha256=XuGewoX6nOch2caSomeCBM4NZRpNG-Pkd7ZOZ_WsKdI,18940
onnxruntime/transformers/onnx_model_bert_tf.py,sha256=5dfNx09iB_9_IX2xf3ZHfFYalMOaYzpwFqOOWYrLYnE,25433
onnxruntime/transformers/onnx_model_clip.py,sha256=F8tQrTwQH5691Lx6LdLJhYxfeHdrcBQg58Q8DTEyGks,1297
onnxruntime/transformers/onnx_model_conformer.py,sha256=yEBXH4eRP7-3VqRN7EAMMTraasfpxXwdpEV99ek3oYw,1444
onnxruntime/transformers/onnx_model_gpt2.py,sha256=3LmzgHuLvO5tyNHKWGidttyqrcpIE7aLBYbRqzjolUg,3913
onnxruntime/transformers/onnx_model_phi.py,sha256=wgCktbyZLu6B_8A5ENPYpUpnYHZMQZWNULPOINj20Ms,36377
onnxruntime/transformers/onnx_model_t5.py,sha256=d3jHUWdEhEr0ugo_N4F_egp_IMmLD7jYz6dfBT48fQw,28931
onnxruntime/transformers/onnx_model_tnlr.py,sha256=2Y5l3DzuHHikd1taUZYhppES0D59UPciOvlyquNiJJE,8436
onnxruntime/transformers/onnx_model_unet.py,sha256=y5OJmrCEtQq2mvlYOHEPbIkVaZgUtkEOCUxK5wlgll4,9517
onnxruntime/transformers/onnx_model_vae.py,sha256=W1Adx9YYwLhc5CSu3Ykgng2MtCn-OCMeFRVUmMeeY28,1545
onnxruntime/transformers/onnx_utils.py,sha256=MFOmBYBWeMNPiQhIaGPEiBvTRMofytW_wdUH6UGb7RI,2161
onnxruntime/transformers/optimizer.py,sha256=llwRmYOXkHBwWgtJpwNjZVXA92LK0Q8Rl66ZKeILdZE,25221
onnxruntime/transformers/profiler.py,sha256=Igx_QE-rVBL17UJlia0RP-QXYtM9AYuUt9gpN9GlsNc,24891
onnxruntime/transformers/quantize_helper.py,sha256=wyVGd_PquMTf0oxA0iLZmfHEhdAEuPk5CTMKMyQcLrE,2885
onnxruntime/transformers/shape_infer_helper.py,sha256=Y9RGSB75pGEmFlDS4X8LgivfqMupeE3NG503raVL45E,4591
onnxruntime/transformers/shape_optimizer.py,sha256=50Loam-3xEYP-Y5h6FP173KHoPi_FtU1wnQBEOkfZeU,15505
onnxruntime/transformers/torch_onnx_export_helper.py,sha256=DOTqWF9DEbxsxqKWtq3NCqcA7de-JSMgjS-MyczJimg,2575
